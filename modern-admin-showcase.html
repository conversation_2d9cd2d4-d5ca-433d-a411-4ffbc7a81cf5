<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Admin Dashboard - The Project Faith</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/admin.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- Modern Sidebar -->
        <aside class="admin-sidebar" id="adminSidebar">
            <div class="sidebar-header">
                <a href="admin-dashboard.html" class="sidebar-logo">VAITH</a>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="admin-dashboard.html" class="nav-link active">
                        <i class="nav-icon fas fa-chart-line"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-users.html" class="nav-link">
                        <i class="nav-icon fas fa-users"></i>
                        <span class="nav-text">Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-products.html" class="nav-link">
                        <i class="nav-icon fas fa-box"></i>
                        <span class="nav-text">Products</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-orders.html" class="nav-link">
                        <i class="nav-icon fas fa-shopping-cart"></i>
                        <span class="nav-text">Orders</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="admin-analytics.html" class="nav-link">
                        <i class="nav-icon fas fa-chart-bar"></i>
                        <span class="nav-text">Analytics</span>
                    </a>
                </div>
                
                <!-- Navigation Divider -->
                <div style="margin: 1.5rem 1rem; border-top: 1px solid var(--admin-border); opacity: 0.5;"></div>
                
                <!-- Quick Links Section -->
                <div style="padding: 0 1rem; margin-bottom: 1rem;">
                    <div style="font-size: 0.75rem; font-weight: 600; color: var(--admin-text-tertiary); text-transform: uppercase; letter-spacing: 0.05em; margin-bottom: 0.75rem;">
                        Quick Links
                    </div>
                </div>
                
                <div class="nav-item">
                    <a href="index.html" class="nav-link" target="_blank">
                        <i class="nav-icon fas fa-external-link-alt"></i>
                        <span class="nav-text">View Store</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="user-settings.html" class="nav-link">
                        <i class="nav-icon fas fa-cog"></i>
                        <span class="nav-text">Settings</span>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="admin-main" id="adminMain">
            <!-- Modern Header -->
            <header class="admin-header">
                <div class="admin-header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="admin-search">
                        <i class="admin-search-icon fas fa-search"></i>
                        <input type="text" class="admin-search-input" placeholder="Search anything...">
                    </div>
                </div>
                <div class="admin-header-right">
                    <button class="nav-icon theme-toggle" id="themeToggle" title="Toggle dark mode">
                        <i class="fas fa-moon" id="themeIcon"></i>
                    </button>
                    <button class="nav-icon" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="badge">3</span>
                    </button>
                    <div class="user-menu">
                        <div class="profile-avatar" id="userMenuBtn">
                            AD
                        </div>
                        <div class="dropdown-menu" id="userDropdown">
                            <a href="user-profile.html" class="dropdown-item">
                                <i class="fas fa-user"></i> Profile
                            </a>
                            <a href="user-settings.html" class="dropdown-item">
                                <i class="fas fa-cog"></i> Settings
                            </a>
                            <hr class="dropdown-divider">
                            <a href="#" class="dropdown-item" id="logoutBtn">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="admin-content">
                <!-- Breadcrumb Navigation -->
                <nav class="breadcrumb fade-in">
                    <div class="breadcrumb-item">
                        <a href="admin-dashboard.html" class="breadcrumb-link">
                            <i class="fas fa-home"></i> Admin
                        </a>
                    </div>
                    <div class="breadcrumb-item">
                        <span class="breadcrumb-current">Modern Dashboard Showcase</span>
                    </div>
                </nav>

                <!-- Welcome Section -->
                <div class="page-header fade-in">
                    <h2 class="page-title">🚀 Modern Admin Dashboard</h2>
                    <p class="page-subtitle">Experience the future of admin interfaces with our redesigned, professional dashboard featuring modern UI/UX patterns, enhanced accessibility, and improved performance.</p>
                </div>

                <!-- Modern Dashboard Cards -->
                <div class="dashboard-grid">
                    <div class="dashboard-card fade-in" style="animation-delay: 0.1s;">
                        <div class="card-header">
                            <div class="card-icon users">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="card-content">
                                <h3>2,847</h3>
                                <p>Total Users</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+12.5%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card fade-in" style="animation-delay: 0.2s;">
                        <div class="card-header">
                            <div class="card-icon orders">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="card-content">
                                <h3>1,429</h3>
                                <p>Total Orders</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+8.2%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card fade-in" style="animation-delay: 0.3s;">
                        <div class="card-header">
                            <div class="card-icon revenue">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="card-content">
                                <h3>$89,247</h3>
                                <p>Total Revenue</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+15.3%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card fade-in" style="animation-delay: 0.4s;">
                        <div class="card-header">
                            <div class="card-icon products">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="card-content">
                                <h3>342</h3>
                                <p>Total Products</p>
                                <div class="card-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    <span>+5.7%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modern Features Showcase -->
                <div class="dashboard-grid" style="margin-top: 2rem; grid-template-columns: 2fr 1fr;">
                    <!-- Modern Data Table -->
                    <div class="data-table-container fade-in" style="animation-delay: 0.5s;">
                        <div class="table-header">
                            <h3 class="table-title">📊 Recent Activity</h3>
                            <div class="table-actions">
                                <button class="btn btn-secondary btn-sm" onclick="showToast('Data refreshed successfully!', 'success')">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                                <button class="btn btn-primary btn-sm" onclick="showToast('Export feature coming soon!', 'info')">
                                    <i class="fas fa-download"></i> Export
                                </button>
                            </div>
                        </div>
                        <div class="table-wrapper">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th style="min-width: 150px;">User</th>
                                        <th style="min-width: 150px;">Action</th>
                                        <th style="min-width: 100px;">Status</th>
                                        <th style="min-width: 120px;">Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                <tr class="slide-in">
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                                            <div class="profile-avatar" style="width: 32px; height: 32px; font-size: 0.75rem;">
                                                JD
                                            </div>
                                            <span style="font-weight: 600;">John Doe</span>
                                        </div>
                                    </td>
                                    <td>Created new order</td>
                                    <td><span class="status-badge status-completed">Completed</span></td>
                                    <td>2 minutes ago</td>
                                </tr>
                                <tr class="slide-in" style="animation-delay: 0.1s;">
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                                            <div class="profile-avatar" style="width: 32px; height: 32px; font-size: 0.75rem;">
                                                SM
                                            </div>
                                            <span style="font-weight: 600;">Sarah Miller</span>
                                        </div>
                                    </td>
                                    <td>Updated profile</td>
                                    <td><span class="status-badge status-active">Active</span></td>
                                    <td>5 minutes ago</td>
                                </tr>
                                <tr class="slide-in" style="animation-delay: 0.2s;">
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                                            <div class="profile-avatar" style="width: 32px; height: 32px; font-size: 0.75rem;">
                                                MJ
                                            </div>
                                            <span style="font-weight: 600;">Mike Johnson</span>
                                        </div>
                                    </td>
                                    <td>Payment processed</td>
                                    <td><span class="status-badge status-pending">Pending</span></td>
                                    <td>8 minutes ago</td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Quick Actions Panel -->
                    <div class="dashboard-card fade-in" style="animation-delay: 0.6s;">
                        <div class="card-header">
                            <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);">
                                <i class="fas fa-bolt"></i>
                            </div>
                            <div class="card-content">
                                <h4 style="margin: 0; color: var(--admin-text-primary);">⚡ Quick Actions</h4>
                                <p style="margin: 0.5rem 0; color: var(--admin-text-secondary); font-size: 0.875rem;">Streamline your workflow</p>
                            </div>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 0.75rem; margin-top: 1rem;">
                            <button class="btn btn-primary btn-sm" onclick="showToast('Feature demo: Add Product', 'info')">
                                <i class="fas fa-plus"></i> Add Product
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="showToast('Feature demo: Manage Users', 'info')">
                                <i class="fas fa-users-cog"></i> Manage Users
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="showToast('Feature demo: View Reports', 'info')">
                                <i class="fas fa-chart-line"></i> View Reports
                            </button>
                            <button class="btn btn-secondary btn-sm" onclick="showToast('Feature demo: System Settings', 'info')">
                                <i class="fas fa-cog"></i> Settings
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Modern Form Showcase -->
                <div class="data-table-container fade-in" style="animation-delay: 0.7s; margin-top: 2rem;">
                    <div class="table-header">
                        <h3 class="table-title">🎨 Modern Form Components</h3>
                        <div class="table-actions">
                            <span class="modern-badge success">Interactive Demo</span>
                        </div>
                    </div>
                    <div style="padding: 2rem; display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                        <div class="form-floating">
                            <input type="text" class="form-input" id="floatingInput" placeholder=" ">
                            <label for="floatingInput" class="form-label">Floating Label Input</label>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Standard Select</label>
                            <select class="form-select">
                                <option>Choose an option...</option>
                                <option>Option 1</option>
                                <option>Option 2</option>
                            </select>
                        </div>
                        <div class="form-floating">
                            <textarea class="form-textarea" id="floatingTextarea" placeholder=" "></textarea>
                            <label for="floatingTextarea" class="form-label">Floating Label Textarea</label>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <button class="btn btn-primary" onclick="showToast('Form submitted successfully!', 'success')">
                                <i class="fas fa-check"></i> Submit Form
                            </button>
                            <button class="btn btn-secondary" onclick="showToast('Form reset!', 'warning')">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Scripts -->
    <script src="js/theme-toggle.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>

    <script>
        // Initialize modern dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize dashboard
            initializeModernDashboard();

            // Setup event listeners
            setupEventListeners();

            // Show welcome message
            setTimeout(() => {
                showToast('🎉 Welcome to the Modern Admin Dashboard! This showcases the redesigned interface with enhanced UX patterns.', 'success', 5000);
            }, 1000);
        });

        function initializeModernDashboard() {
            // Animate dashboard cards on load
            const cards = document.querySelectorAll('.dashboard-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        }

        function setupEventListeners() {
            // Sidebar toggle
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                const sidebar = document.getElementById('adminSidebar');
                const main = document.getElementById('adminMain');

                sidebar.classList.toggle('collapsed');
                main.classList.toggle('expanded');

                showToast('Sidebar toggled', 'info', 2000);
            });

            // User menu toggle
            document.getElementById('userMenuBtn').addEventListener('click', function(e) {
                e.stopPropagation();
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function() {
                const dropdown = document.getElementById('userDropdown');
                dropdown.classList.remove('show');
            });

            // Search functionality demo
            const searchInput = document.querySelector('.admin-search-input');
            searchInput.addEventListener('input', function(e) {
                if (e.target.value.length > 2) {
                    showToast(`Searching for: "${e.target.value}"`, 'info', 2000);
                }
            });

            // Logout
            document.getElementById('logoutBtn').addEventListener('click', function(e) {
                e.preventDefault();
                showToast('Logging out...', 'info');
                setTimeout(() => {
                    window.location.href = 'login.html';
                }, 1500);
            });
        }

        // Modern Toast Notification System
        function showToast(message, type = 'info', duration = 3000) {
            const toastContainer = getOrCreateToastContainer();

            const toast = document.createElement('div');
            toast.className = `toast ${type}`;

            const icons = {
                success: '✅',
                error: '❌',
                warning: '⚠️',
                info: '💡'
            };

            const titles = {
                success: 'Success',
                error: 'Error',
                warning: 'Warning',
                info: 'Info'
            };

            toast.innerHTML = `
                <div class="toast-header">
                    <div class="toast-title">
                        ${icons[type]} ${titles[type]}
                    </div>
                    <button class="toast-close" onclick="removeToast(this.parentElement.parentElement)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="toast-message">${message}</div>
            `;

            toastContainer.appendChild(toast);

            // Auto remove after duration
            setTimeout(() => {
                removeToast(toast);
            }, duration);
        }

        function getOrCreateToastContainer() {
            let container = document.querySelector('.toast-container');
            if (!container) {
                container = document.createElement('div');
                container.className = 'toast-container';
                document.body.appendChild(container);
            }
            return container;
        }

        function removeToast(toast) {
            if (toast && toast.parentElement) {
                toast.style.animation = 'slideOutRight 0.3s ease-in forwards';
                setTimeout(() => {
                    if (toast.parentElement) {
                        toast.parentElement.removeChild(toast);
                    }
                }, 300);
            }
        }

        // Add slideOutRight animation to CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideOutRight {
                from {
                    opacity: 1;
                    transform: translateX(0);
                }
                to {
                    opacity: 0;
                    transform: translateX(100%);
                }
            }

            .dashboard-card {
                opacity: 0;
                transform: translateY(20px);
                transition: all 0.5s ease;
            }
        `;
        document.head.appendChild(style);

        // Demo functions for interactive elements
        function demoFeature(featureName) {
            showToast(`${featureName} feature demonstrated!`, 'success');
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + K for search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                document.querySelector('.admin-search-input').focus();
                showToast('Search activated! (Ctrl/Cmd + K)', 'info', 2000);
            }

            // Ctrl/Cmd + B for sidebar toggle
            if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
                e.preventDefault();
                document.getElementById('sidebarToggle').click();
            }
        });

        // Show keyboard shortcuts info
        setTimeout(() => {
            showToast('💡 Tip: Use Ctrl/Cmd + K to search, Ctrl/Cmd + B to toggle sidebar', 'info', 4000);
        }, 3000);
    </script>
</body>
</html>
