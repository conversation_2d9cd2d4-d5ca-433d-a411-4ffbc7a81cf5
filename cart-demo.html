<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cart Functionality Demo</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .demo-section {
            margin: 2rem 0;
            padding: 2rem;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .status-indicator {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .status-default { background: #e3f2fd; color: #1976d2; }
        .status-in-cart { background: #ffebee; color: #d32f2f; }
        .demo-controls {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container" style="padding: 2rem;">
        <h1>Cart Functionality Demo</h1>
        <p>This demo shows the new cart button behavior: clicking "Add to Cart" adds the item, clicking "Remove from Cart" removes it.</p>

        <div class="demo-section">
            <h2>Current Behavior</h2>
            <div id="current-status" class="status-indicator status-default">
                Default State: Button shows "Add to Cart" with shopping cart icon
            </div>

            <!-- Sample Product Card -->
            <div class="products-grid" style="max-width: 300px;">
                <div class="product-card" data-product-id="888">
                    <div class="product-image">
                        <img src="https://via.placeholder.com/300x400" alt="Demo Product">
                        <div class="product-actions">
                            <button class="action-btn favorite-btn" data-product-id="888" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                            <button class="action-btn quick-view-btn" data-product-id="888" title="Quick View">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Demo Product</h3>
                        <div class="product-price">
                            <span class="current-price">$59.99</span>
                        </div>
                        <div class="product-card-actions">
                            <button class="btn btn-primary add-to-cart-btn" data-product-id="888">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-controls">
                <button onclick="addToCartDemo()" class="btn btn-primary">Add to Cart</button>
                <button onclick="removeFromCartDemo()" class="btn btn-secondary">Remove from Cart</button>
                <button onclick="checkCartStatus()" class="btn btn-outline">Check Status</button>
            </div>
        </div>

        <div class="demo-section">
            <h2>Expected Behavior</h2>
            <ul>
                <li><strong>Default State:</strong> Button shows "Add to Cart" with shopping cart icon and blue background</li>
                <li><strong>Added to Cart:</strong> Button shows "Remove from Cart" with X icon and red background (#dc3545)</li>
                <li><strong>Clicking "Add to Cart":</strong> Adds product to cart and changes button to "Remove from Cart"</li>
                <li><strong>Clicking "Remove from Cart":</strong> Removes product from cart and changes button back to "Add to Cart"</li>
                <li><strong>Hover Effects:</strong> Red button becomes darker red on hover</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>Storage Status</h2>
            <div id="storage-status">
                <p><strong>Cart Array:</strong> <span id="cart-array">[]</span></p>
                <p><strong>Product 888 in Cart:</strong> <span id="product-status">No</span></p>
                <p><strong>Cart Count:</strong> <span id="cart-count">0</span></p>
            </div>
        </div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/favorites.js"></script>
    
    <script>
        const demoProduct = {
            id: 888,
            title: 'Demo Product',
            price: 59.99,
            image: 'https://via.placeholder.com/300x400'
        };
        
        function addToCartDemo() {
            addToCart(demoProduct);
            updateStatus();
        }
        
        function removeFromCartDemo() {
            removeFromCartByProductId(888);
            updateStatus();
        }
        
        function checkCartStatus() {
            updateStatus();
            
            // Check button state
            const cartBtn = document.querySelector('.add-to-cart-btn[data-product-id="888"]');
            
            console.log('Cart button text:', cartBtn.textContent);
            console.log('Cart button background:', cartBtn.style.background);
            console.log('Cart button class:', cartBtn.className);
            console.log('Cart button title:', cartBtn.title);
        }
        
        function updateStatus() {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            const isInCart = cart.some(item => item.id === 888);
            const cartCount = cart.reduce((total, item) => total + item.quantity, 0);
            
            // Update storage display
            document.getElementById('cart-array').textContent = JSON.stringify(cart);
            document.getElementById('product-status').textContent = isInCart ? 'Yes' : 'No';
            document.getElementById('cart-count').textContent = cartCount;
            
            // Update status indicator
            const statusDiv = document.getElementById('current-status');
            if (isInCart) {
                statusDiv.className = 'status-indicator status-in-cart';
                statusDiv.textContent = 'In Cart State: Button shows "Remove from Cart" with X icon and red background';
            } else {
                statusDiv.className = 'status-indicator status-default';
                statusDiv.textContent = 'Default State: Button shows "Add to Cart" with shopping cart icon and blue background';
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listener to the cart button
            document.querySelector('.add-to-cart-btn[data-product-id="888"]').addEventListener('click', function() {
                const cart = JSON.parse(localStorage.getItem('cart')) || [];
                const isInCart = cart.some(item => item.id === 888);
                
                if (isInCart) {
                    removeFromCartByProductId(888);
                } else {
                    addToCart(demoProduct);
                }
                updateStatus();
            });
            
            // Initial state
            setTimeout(() => {
                updateCartButtons();
                updateStatus();
            }, 100);
        });
    </script>
</body>
</html>
