<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Add Product Modal</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #4B0082;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        .debug-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🐛 Debug Add Product Modal</h1>
        <p>This page helps debug the Add Product modal functionality.</p>

        <div class="test-section">
            <h3>🔍 Element Detection</h3>
            <button class="btn btn-info" onclick="checkElements()">Check Modal Elements</button>
            <div id="elementCheck" class="debug-output"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Function Tests</h3>
            <button class="btn btn-primary" onclick="testShowModal()">Test Show Modal Function</button>
            <button class="btn btn-success" onclick="testDirectModal()">Test Direct Modal Display</button>
            <div id="functionTest" class="debug-output"></div>
        </div>

        <div class="test-section">
            <h3>📝 Console Output</h3>
            <button class="btn btn-info" onclick="clearConsole()">Clear Console Output</button>
            <div id="consoleOutput" class="debug-output">Console messages will appear here...</div>
        </div>

        <div class="test-section">
            <h3>🔗 Navigation</h3>
            <button class="btn btn-primary" onclick="openAdminPage()">Open Admin Products Page</button>
        </div>
    </div>

    <script>
        // Capture console messages
        const originalLog = console.log;
        const originalError = console.error;
        let consoleMessages = [];

        console.log = function(...args) {
            consoleMessages.push('LOG: ' + args.join(' '));
            updateConsoleOutput();
            originalLog.apply(console, args);
        };

        console.error = function(...args) {
            consoleMessages.push('ERROR: ' + args.join(' '));
            updateConsoleOutput();
            originalError.apply(console, args);
        };

        function updateConsoleOutput() {
            const output = document.getElementById('consoleOutput');
            output.textContent = consoleMessages.slice(-20).join('\n'); // Show last 20 messages
        }

        function clearConsole() {
            consoleMessages = [];
            updateConsoleOutput();
        }

        function checkElements() {
            const output = document.getElementById('elementCheck');
            let result = 'Element Check Results:\n\n';

            const elements = [
                'addProductModal',
                'overlay',
                'addProductForm',
                'closeAddProductModal',
                'cancelAddProduct',
                'productName',
                'productCategory',
                'productPrice',
                'productStock'
            ];

            elements.forEach(id => {
                const element = document.getElementById(id);
                result += `${id}: ${element ? '✅ Found' : '❌ Not Found'}\n`;
            });

            // Check if functions exist
            result += '\nFunction Check:\n';
            result += `showAddProductModal: ${typeof window.showAddProductModal !== 'undefined' ? '✅ Exists' : '❌ Not Found'}\n`;
            result += `closeAddProductModal: ${typeof window.closeAddProductModal !== 'undefined' ? '✅ Exists' : '❌ Not Found'}\n`;

            output.textContent = result;
        }

        function testShowModal() {
            const output = document.getElementById('functionTest');
            output.textContent = 'Testing showAddProductModal function...\n';

            try {
                if (typeof window.showAddProductModal === 'function') {
                    window.showAddProductModal();
                    output.textContent += '✅ Function called successfully\n';
                } else {
                    output.textContent += '❌ Function not found\n';
                }
            } catch (error) {
                output.textContent += `❌ Error: ${error.message}\n`;
            }
        }

        function testDirectModal() {
            const output = document.getElementById('functionTest');
            output.textContent = 'Testing direct modal display...\n';

            try {
                const modal = document.getElementById('addProductModal');
                const overlay = document.getElementById('overlay');

                if (modal && overlay) {
                    modal.style.display = 'flex';
                    overlay.style.display = 'block';
                    output.textContent += '✅ Modal displayed directly\n';
                } else {
                    output.textContent += `❌ Elements not found - Modal: ${!!modal}, Overlay: ${!!overlay}\n`;
                }
            } catch (error) {
                output.textContent += `❌ Error: ${error.message}\n`;
            }
        }

        function openAdminPage() {
            window.open('admin-products.html', '_blank');
        }

        // Auto-check elements on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkElements, 500);
        });
    </script>
</body>
</html>
