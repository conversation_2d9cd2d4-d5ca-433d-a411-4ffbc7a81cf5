<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favorites Functionality Demo</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .demo-section {
            margin: 2rem 0;
            padding: 2rem;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .status-indicator {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .status-default { background: #e3f2fd; color: #1976d2; }
        .status-active { background: #f3e5f5; color: #7b1fa2; }
        .demo-controls {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container" style="padding: 2rem;">
        <h1>Favorites Functionality Demo</h1>
        <p>This demo shows exactly how the favorites functionality works in the Featured Products section.</p>

        <div class="demo-section">
            <h2>Current Behavior</h2>
            <div id="current-status" class="status-indicator status-default">
                Default State: Heart icon is empty (far fa-heart), color is default
            </div>

            <!-- Sample Product Card -->
            <div class="products-grid" style="max-width: 300px;">
                <div class="product-card" data-product-id="999">
                    <div class="product-image">
                        <img src="https://via.placeholder.com/300x400" alt="Demo Product">
                        <div class="product-actions">
                            <button class="action-btn favorite-btn" data-product-id="999" title="Add to Favorites">
                                <i class="far fa-heart"></i>
                            </button>
                            <button class="action-btn quick-view-btn" data-product-id="999" title="Quick View">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">Demo Product</h3>
                        <div class="product-price">
                            <span class="current-price">$49.99</span>
                        </div>
                        <div class="product-card-actions">
                            <button class="btn btn-primary add-to-cart-btn" data-product-id="999">
                                <i class="fas fa-shopping-cart"></i>
                                Add to Cart
                            </button>
                            <button class="btn btn-outline favorite-btn-text" data-product-id="999">
                                <i class="far fa-heart"></i>
                                Add to Favorites
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="demo-controls">
                <button onclick="addToFavorites()" class="btn btn-primary">Add to Favorites</button>
                <button onclick="removeFromFavorites()" class="btn btn-secondary">Remove from Favorites</button>
                <button onclick="checkStatus()" class="btn btn-outline">Check Status</button>
            </div>
        </div>

        <div class="demo-section">
            <h2>Expected Behavior</h2>
            <ul>
                <li><strong>Default State:</strong> Empty heart icon (far fa-heart) with default color</li>
                <li><strong>Added to Favorites:</strong> Filled heart icon (fas fa-heart) with purple color (#4B0082)</li>
                <li><strong>Removed from Favorites:</strong> Returns to empty heart icon with default color</li>
                <li><strong>Text Button:</strong> Changes text from "Add to Favorites" to "Remove from Favorites" and background becomes purple</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>Storage Status</h2>
            <div id="storage-status">
                <p><strong>Favorites Array:</strong> <span id="favorites-array">[]</span></p>
                <p><strong>Product 999 in Favorites:</strong> <span id="product-status">No</span></p>
            </div>
        </div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/favorites.js"></script>
    
    <script>
        function addToFavorites() {
            toggleFavorite(999, 'Demo Product');
            updateStatus();
        }
        
        function removeFromFavorites() {
            const favorites = JSON.parse(localStorage.getItem('favorites')) || [];
            if (favorites.includes(999)) {
                toggleFavorite(999, 'Demo Product');
            }
            updateStatus();
        }
        
        function checkStatus() {
            updateStatus();
            
            // Check button states
            const favoriteBtn = document.querySelector('.favorite-btn[data-product-id="999"]');
            const favoriteBtnText = document.querySelector('.favorite-btn-text[data-product-id="999"]');
            const icon = favoriteBtn.querySelector('i');
            
            console.log('Favorite button icon class:', icon.className);
            console.log('Favorite button color:', favoriteBtn.style.color);
            console.log('Favorite text button background:', favoriteBtnText.style.background);
            console.log('Favorite text button text:', favoriteBtnText.textContent);
        }
        
        function updateStatus() {
            const favorites = JSON.parse(localStorage.getItem('favorites')) || [];
            const isInFavorites = favorites.includes(999);
            
            // Update storage display
            document.getElementById('favorites-array').textContent = JSON.stringify(favorites);
            document.getElementById('product-status').textContent = isInFavorites ? 'Yes' : 'No';
            
            // Update status indicator
            const statusDiv = document.getElementById('current-status');
            if (isInFavorites) {
                statusDiv.className = 'status-indicator status-active';
                statusDiv.textContent = 'Active State: Heart icon is filled (fas fa-heart), color is purple (#4B0082)';
            } else {
                statusDiv.className = 'status-indicator status-default';
                statusDiv.textContent = 'Default State: Heart icon is empty (far fa-heart), color is default';
            }
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners
            document.querySelector('.favorite-btn[data-product-id="999"]').addEventListener('click', function() {
                toggleFavorite(999, 'Demo Product');
                updateStatus();
            });
            
            document.querySelector('.favorite-btn-text[data-product-id="999"]').addEventListener('click', function() {
                toggleFavorite(999, 'Demo Product');
                updateStatus();
            });
            
            // Initial state
            setTimeout(() => {
                updateFavoriteButtons();
                updateStatus();
            }, 100);
        });
    </script>
</body>
</html>
