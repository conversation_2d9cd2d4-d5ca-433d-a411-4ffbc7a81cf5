<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Order ID Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .order-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
        }
        .order-item {
            padding: 5px;
            border-bottom: 1px solid #eee;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Order ID Generation Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Check Current Order IDs</h2>
        <p>View existing order IDs to see the current format.</p>
        <button onclick="viewCurrentOrders()">View Current Orders</button>
        <div id="currentOrdersResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Generate New Order IDs</h2>
        <p>Create multiple orders to test the sequential ID generation.</p>
        <button onclick="generateMultipleOrders()">Generate 5 Test Orders</button>
        <div id="generateOrdersResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: View All Orders</h2>
        <p>View all orders (pending and existing) with their IDs.</p>
        <button onclick="viewAllOrders()">View All Orders</button>
        <div id="allOrdersResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 4: Clear All Orders</h2>
        <p>Clear all orders to test starting from ord_001 again.</p>
        <button onclick="clearAllOrders()" style="background-color: #dc3545;">Clear All Orders</button>
        <div id="clearOrdersResult"></div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
    <script>
        // Initialize admin manager
        const adminManager = new AdminManager();

        function viewCurrentOrders() {
            const pendingOrders = adminManager.getAllPendingOrders();
            const existingOrders = adminManager.getAllOrders();
            const allOrders = [...pendingOrders, ...existingOrders];
            
            const resultDiv = document.getElementById('currentOrdersResult');
            
            if (allOrders.length === 0) {
                resultDiv.innerHTML = '<div class="test-result">No orders found.</div>';
            } else {
                const ordersList = allOrders.map(order => 
                    `<div class="order-item">${order.id} - ${order.customerName} - ${order.status}</div>`
                ).join('');
                resultDiv.innerHTML = 
                    `<div class="test-result success">Found ${allOrders.length} orders:<div class="order-list">${ordersList}</div></div>`;
            }
        }

        function generateMultipleOrders() {
            const resultDiv = document.getElementById('generateOrdersResult');
            const generatedOrders = [];
            
            try {
                for (let i = 1; i <= 5; i++) {
                    const orderData = {
                        customerName: `Test Customer ${i}`,
                        customerEmail: `test${i}@example.com`,
                        customerPhone: `+123456789${i}`,
                        shippingAddress: `${i}23 Test Street, Test City, TC 1234${i}`,
                        total: 99.99 + i,
                        items: [
                            {
                                id: `test-product-${i}`,
                                name: `Test Product ${i}`,
                                price: 99.99 + i,
                                quantity: 1,
                                image: 'https://via.placeholder.com/150'
                            }
                        ]
                    };

                    const order = adminManager.createOrder(orderData);
                    generatedOrders.push(order);
                }

                const ordersList = generatedOrders.map(order => 
                    `<div class="order-item">${order.id} - ${order.customerName}</div>`
                ).join('');
                
                resultDiv.innerHTML = 
                    `<div class="test-result success">Generated ${generatedOrders.length} orders:<div class="order-list">${ordersList}</div></div>`;
                    
            } catch (error) {
                resultDiv.innerHTML = 
                    `<div class="test-result error">Error generating orders: ${error.message}</div>`;
            }
        }

        function viewAllOrders() {
            const pendingOrders = adminManager.getAllPendingOrders();
            const existingOrders = adminManager.getAllOrders();
            const resultDiv = document.getElementById('allOrdersResult');
            
            let content = '<div class="test-result success">';
            
            if (pendingOrders.length > 0) {
                const pendingList = pendingOrders.map(order => 
                    `<div class="order-item">${order.id} - ${order.customerName} - PENDING</div>`
                ).join('');
                content += `<h4>Pending Orders (${pendingOrders.length}):</h4><div class="order-list">${pendingList}</div>`;
            }
            
            if (existingOrders.length > 0) {
                const existingList = existingOrders.map(order => 
                    `<div class="order-item">${order.id} - ${order.customerName} - ${order.status.toUpperCase()}</div>`
                ).join('');
                content += `<h4>Existing Orders (${existingOrders.length}):</h4><div class="order-list">${existingList}</div>`;
            }
            
            if (pendingOrders.length === 0 && existingOrders.length === 0) {
                content = '<div class="test-result">No orders found.</div>';
            } else {
                content += '</div>';
            }
            
            resultDiv.innerHTML = content;
        }

        function clearAllOrders() {
            if (confirm('Are you sure you want to clear all orders? This cannot be undone.')) {
                // Clear localStorage
                localStorage.removeItem('vaith_orders');
                localStorage.removeItem('vaith_pending_orders');
                
                // Reinitialize admin manager
                location.reload();
            }
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            viewCurrentOrders();
        });
    </script>
</body>
</html>
