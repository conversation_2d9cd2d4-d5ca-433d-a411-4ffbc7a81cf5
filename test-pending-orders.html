<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pending Orders</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .approve-btn {
            background-color: #28a745;
        }
        .approve-btn:hover {
            background-color: #1e7e34;
        }
        .reject-btn {
            background-color: #dc3545;
        }
        .reject-btn:hover {
            background-color: #c82333;
        }
    </style>
</head>
<body>
    <h1>Pending Orders System Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Create a New Order</h2>
        <p>This will create a new order that should go to pending orders first.</p>
        <button onclick="createTestOrder()">Create Test Order</button>
        <div id="createOrderResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: View Pending Orders</h2>
        <button onclick="viewPendingOrders()">View Pending Orders</button>
        <div id="pendingOrdersResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Approve Pending Order</h2>
        <p>Select a pending order to approve:</p>
        <select id="pendingOrderSelect">
            <option value="">Select an order...</option>
        </select>
        <button class="approve-btn" onclick="approvePendingOrder()">Approve Order</button>
        <div id="approveOrderResult"></div>
    </div>

    <div class="test-section">
        <h2>Test 4: View Existing Orders</h2>
        <button onclick="viewExistingOrders()">View Existing Orders</button>
        <div id="existingOrdersResult"></div>
    </div>

    <script src="js/auth.js"></script>
    <script src="js/admin.js"></script>
    <script>
        // Initialize admin manager
        const adminManager = new AdminManager();

        function createTestOrder() {
            const orderData = {
                customerName: 'Test Customer',
                customerEmail: '<EMAIL>',
                customerPhone: '+1234567890',
                shippingAddress: '123 Test Street, Test City, TC 12345',
                total: 99.99,
                items: [
                    {
                        id: 'test-product',
                        name: 'Test Product',
                        price: 99.99,
                        quantity: 1,
                        image: 'https://via.placeholder.com/150'
                    }
                ]
            };

            try {
                const order = adminManager.createOrder(orderData);
                document.getElementById('createOrderResult').innerHTML =
                    `<div class="test-result success">Order created successfully! Order ID: ${order.id} (Status: ${order.status})</div>`;
                updatePendingOrderSelect();
            } catch (error) {
                document.getElementById('createOrderResult').innerHTML =
                    `<div class="test-result error">Error creating order: ${error.message}</div>`;
            }
        }

        function viewPendingOrders() {
            const pendingOrders = adminManager.getAllPendingOrders();
            const resultDiv = document.getElementById('pendingOrdersResult');
            
            if (pendingOrders.length === 0) {
                resultDiv.innerHTML = '<div class="test-result">No pending orders found.</div>';
            } else {
                const ordersList = pendingOrders.map(order => 
                    `<li>${order.id} - ${order.customerName} - $${order.total.toFixed(2)} - ${order.status}</li>`
                ).join('');
                resultDiv.innerHTML = 
                    `<div class="test-result success">Found ${pendingOrders.length} pending orders:<ul>${ordersList}</ul></div>`;
            }
            updatePendingOrderSelect();
        }

        function updatePendingOrderSelect() {
            const select = document.getElementById('pendingOrderSelect');
            const pendingOrders = adminManager.getAllPendingOrders();
            
            select.innerHTML = '<option value="">Select an order...</option>';
            pendingOrders.forEach(order => {
                const option = document.createElement('option');
                option.value = order.id;
                option.textContent = `${order.id} - ${order.customerName}`;
                select.appendChild(option);
            });
        }

        function approvePendingOrder() {
            const select = document.getElementById('pendingOrderSelect');
            const orderId = select.value;

            if (!orderId) {
                document.getElementById('approveOrderResult').innerHTML =
                    '<div class="test-result error">Please select an order to approve.</div>';
                return;
            }

            try {
                const approvedOrder = adminManager.approvePendingOrder(orderId, 'pending');
                if (approvedOrder) {
                    document.getElementById('approveOrderResult').innerHTML =
                        `<div class="test-result success">Order ${orderId} approved and moved to existing orders with status: ${approvedOrder.status}</div>`;
                    updatePendingOrderSelect();
                } else {
                    document.getElementById('approveOrderResult').innerHTML =
                        `<div class="test-result error">Failed to approve order ${orderId}</div>`;
                }
            } catch (error) {
                document.getElementById('approveOrderResult').innerHTML =
                    `<div class="test-result error">Error approving order: ${error.message}</div>`;
            }
        }

        function viewExistingOrders() {
            const existingOrders = adminManager.getAllOrders();
            const resultDiv = document.getElementById('existingOrdersResult');
            
            if (existingOrders.length === 0) {
                resultDiv.innerHTML = '<div class="test-result">No existing orders found.</div>';
            } else {
                const ordersList = existingOrders.map(order => 
                    `<li>${order.id} - ${order.customerName} - $${order.total.toFixed(2)} - ${order.status}</li>`
                ).join('');
                resultDiv.innerHTML = 
                    `<div class="test-result success">Found ${existingOrders.length} existing orders:<ul>${ordersList}</ul></div>`;
            }
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            updatePendingOrderSelect();
        });
    </script>
</body>
</html>
