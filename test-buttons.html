<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button State Test</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container" style="padding: 2rem;">
        <h1>Button State Test</h1>
        
        <div class="products-grid" style="max-width: 800px;">
            <!-- Test Product Card 1 -->
            <div class="product-card" data-product-id="1">
                <div class="product-image">
                    <img src="https://via.placeholder.com/300x400" alt="Test Product 1">
                    <div class="product-actions">
                        <button class="action-btn favorite-btn" data-product-id="1" title="Add to Favorites">
                            <i class="far fa-heart"></i>
                        </button>
                        <button class="action-btn quick-view-btn" data-product-id="1" title="Quick View">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-title">Test Product 1</h3>
                    <div class="product-price">
                        <span class="current-price">$29.99</span>
                    </div>
                    <div class="product-card-actions">
                        <button class="btn btn-primary add-to-cart-btn" data-product-id="1">
                            <i class="fas fa-shopping-cart"></i>
                            Add to Cart
                        </button>
                        <button class="btn btn-outline favorite-btn-text" data-product-id="1">
                            <i class="far fa-heart"></i>
                            Add to Favorites
                        </button>
                    </div>
                </div>
            </div>

            <!-- Test Product Card 2 -->
            <div class="product-card" data-product-id="2">
                <div class="product-image">
                    <img src="https://via.placeholder.com/300x400" alt="Test Product 2">
                    <div class="product-actions">
                        <button class="action-btn favorite-btn" data-product-id="2" title="Add to Favorites">
                            <i class="far fa-heart"></i>
                        </button>
                        <button class="action-btn quick-view-btn" data-product-id="2" title="Quick View">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <div class="product-info">
                    <h3 class="product-title">Test Product 2</h3>
                    <div class="product-price">
                        <span class="current-price">$39.99</span>
                    </div>
                    <div class="product-card-actions">
                        <button class="btn btn-primary add-to-cart-btn" data-product-id="2">
                            <i class="fas fa-shopping-cart"></i>
                            Add to Cart
                        </button>
                        <button class="btn btn-outline favorite-btn-text" data-product-id="2">
                            <i class="far fa-heart"></i>
                            Add to Favorites
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div style="margin-top: 2rem;">
            <h3>Test Controls</h3>
            <button onclick="clearStorage()" class="btn btn-secondary">Clear Storage</button>
            <button onclick="showStorage()" class="btn btn-secondary">Show Storage</button>
            <button onclick="testFavorites()" class="btn btn-secondary">Test Favorites</button>
            <button onclick="testCart()" class="btn btn-secondary">Test Cart</button>
        </div>
        
        <div id="storage-info" style="margin-top: 1rem; padding: 1rem; background: #f5f5f5; border-radius: 8px;">
            <h4>Current Storage:</h4>
            <p><strong>Cart:</strong> <span id="cart-content">[]</span></p>
            <p><strong>Favorites:</strong> <span id="favorites-content">[]</span></p>
        </div>
    </div>

    <!-- Include necessary scripts -->
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script src="js/favorites.js"></script>
    
    <script>
        // Test functions
        function clearStorage() {
            localStorage.removeItem('cart');
            localStorage.removeItem('favorites');
            updateCartButtons();
            updateFavoriteButtons();
            showStorage();
        }
        
        function showStorage() {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            const favorites = JSON.parse(localStorage.getItem('favorites')) || [];

            document.getElementById('cart-content').textContent = JSON.stringify(cart);
            document.getElementById('favorites-content').textContent = JSON.stringify(favorites);
        }

        function testFavorites() {
            console.log('Testing favorites functionality...');

            // Test adding to favorites
            toggleFavorite(1, 'Test Product 1');
            console.log('Added product 1 to favorites');

            // Check button state
            const btn1 = document.querySelector('[data-product-id="1"].favorite-btn');
            const icon1 = btn1.querySelector('i');
            console.log('Product 1 favorite button icon:', icon1.className);
            console.log('Product 1 favorite button color:', btn1.style.color);

            // Test removing from favorites
            setTimeout(() => {
                toggleFavorite(1, 'Test Product 1');
                console.log('Removed product 1 from favorites');

                // Check button state again
                console.log('Product 1 favorite button icon after removal:', icon1.className);
                console.log('Product 1 favorite button color after removal:', btn1.style.color);

                showStorage();
            }, 1000);
        }

        function testCart() {
            console.log('Testing cart functionality...');

            // Test adding to cart
            const product = {
                id: 2,
                title: 'Test Product 2',
                price: 39.99,
                image: 'https://via.placeholder.com/300x400'
            };
            addToCart(product);
            console.log('Added product 2 to cart');

            // Check button state
            const btn2 = document.querySelector('[data-product-id="2"].add-to-cart-btn');
            console.log('Product 2 cart button text:', btn2.textContent);
            console.log('Product 2 cart button background:', btn2.style.background);

            // Test removing from cart after 2 seconds
            setTimeout(() => {
                console.log('Now testing remove from cart...');
                btn2.click(); // This should remove it from cart
                console.log('Product 2 cart button text after removal:', btn2.textContent);
                console.log('Product 2 cart button background after removal:', btn2.style.background);
                showStorage();
            }, 2000);

            showStorage();
        }
        
        // Initialize test
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners to test buttons
            document.querySelectorAll('.add-to-cart-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const productId = parseInt(this.getAttribute('data-product-id'));
                    const product = {
                        id: productId,
                        title: `Test Product ${productId}`,
                        price: productId === 1 ? 29.99 : 39.99,
                        image: 'https://via.placeholder.com/300x400'
                    };

                    // Check if product is already in cart
                    const cart = JSON.parse(localStorage.getItem('cart')) || [];
                    const isInCart = cart.some(item => item.id === productId);

                    if (isInCart) {
                        // Remove from cart
                        removeFromCartByProductId(productId);
                    } else {
                        // Add to cart
                        addToCart(product);
                    }
                    showStorage();
                });
            });
            
            document.querySelectorAll('.favorite-btn, .favorite-btn-text').forEach(btn => {
                btn.addEventListener('click', function() {
                    const productId = parseInt(this.getAttribute('data-product-id'));
                    toggleFavorite(productId, `Test Product ${productId}`);
                    showStorage();
                });
            });
            
            // Initial state update
            setTimeout(() => {
                updateCartButtons();
                updateFavoriteButtons();
                showStorage();
            }, 100);
        });
    </script>
</body>
</html>
