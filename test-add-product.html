<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add Product Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #4B0082;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Add Product Functionality Test</h1>
        <p>This page tests the enhanced Add Product functionality for the admin dashboard.</p>

        <div class="test-section">
            <h3>📦 Current Products in Storage</h3>
            <button class="btn btn-info" onclick="showCurrentProducts()">Show Current Products</button>
            <button class="btn btn-danger" onclick="clearProducts()">Clear All Products</button>
            <div id="currentProducts" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>➕ Add Test Product</h3>
            <p>This will add a sample product to test the functionality:</p>
            <button class="btn btn-success" onclick="addTestProduct()">Add Test Product</button>
            <div id="addResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔗 Navigation Links</h3>
            <button class="btn btn-primary" onclick="openAdminProducts()">Open Admin Products Page</button>
            <button class="btn btn-primary" onclick="openProductsPage()">Open Products Page (Frontend)</button>
            <button class="btn btn-primary" onclick="openHomePage()">Open Home Page</button>
        </div>

        <div class="test-section">
            <h3>📊 Storage Information</h3>
            <button class="btn btn-info" onclick="showStorageInfo()">Show Storage Info</button>
            <div id="storageInfo" class="result info" style="display: none;"></div>
        </div>
    </div>

    <script>
        function showCurrentProducts() {
            const products = JSON.parse(localStorage.getItem('vaith_products')) || [];
            const resultDiv = document.getElementById('currentProducts');
            
            if (products.length === 0) {
                resultDiv.textContent = 'No products found in storage.';
                resultDiv.className = 'result info';
            } else {
                resultDiv.textContent = `Found ${products.length} products:\n\n` + 
                    products.map((p, i) => `${i + 1}. ${p.name} - $${p.price} (${p.category}) - Stock: ${p.stock}`).join('\n');
                resultDiv.className = 'result success';
            }
            resultDiv.style.display = 'block';
        }

        function clearProducts() {
            if (confirm('Are you sure you want to clear all products?')) {
                localStorage.removeItem('vaith_products');
                document.getElementById('currentProducts').textContent = 'All products cleared!';
                document.getElementById('currentProducts').className = 'result success';
                document.getElementById('currentProducts').style.display = 'block';
            }
        }

        function addTestProduct() {
            const testProduct = {
                id: Date.now(),
                name: 'Test Product - ' + new Date().toLocaleTimeString(),
                brand: 'VAITH',
                description: 'This is a test product created to verify the Add Product functionality.',
                category: 'women',
                status: 'active',
                sku: 'SKU-TEST-' + Date.now(),
                price: 29.99,
                originalPrice: 39.99,
                stock: 50,
                images: ['https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=500&fit=crop&auto=format&q=80'],
                sizes: ['XS', 'S', 'M', 'L', 'XL'],
                colors: ['Red', 'Blue', 'Black'],
                rating: 0,
                reviews: 0,
                createdDate: new Date().toISOString(),
                updatedDate: new Date().toISOString()
            };

            try {
                let products = JSON.parse(localStorage.getItem('vaith_products')) || [];
                products.push(testProduct);
                localStorage.setItem('vaith_products', JSON.stringify(products));

                const resultDiv = document.getElementById('addResult');
                resultDiv.textContent = `✅ Test product added successfully!\n\nProduct Details:\n${JSON.stringify(testProduct, null, 2)}`;
                resultDiv.className = 'result success';
                resultDiv.style.display = 'block';
            } catch (error) {
                const resultDiv = document.getElementById('addResult');
                resultDiv.textContent = `❌ Error adding test product:\n${error.message}`;
                resultDiv.className = 'result error';
                resultDiv.style.display = 'block';
            }
        }

        function openAdminProducts() {
            window.open('admin-products.html', '_blank');
        }

        function openProductsPage() {
            window.open('products.html', '_blank');
        }

        function openHomePage() {
            window.open('index.html', '_blank');
        }

        function showStorageInfo() {
            const storageKeys = ['vaith_products', 'theprojectfaith_products', 'cart', 'favorites', 'vaith_orders'];
            let info = 'LocalStorage Contents:\n\n';
            
            storageKeys.forEach(key => {
                const data = localStorage.getItem(key);
                if (data) {
                    try {
                        const parsed = JSON.parse(data);
                        info += `${key}: ${Array.isArray(parsed) ? parsed.length + ' items' : 'object'}\n`;
                    } catch {
                        info += `${key}: ${data.length} characters (string)\n`;
                    }
                } else {
                    info += `${key}: not found\n`;
                }
            });

            const resultDiv = document.getElementById('storageInfo');
            resultDiv.textContent = info;
            resultDiv.className = 'result info';
            resultDiv.style.display = 'block';
        }

        // Auto-show current products on page load
        document.addEventListener('DOMContentLoaded', function() {
            showCurrentProducts();
            showStorageInfo();
        });
    </script>
</body>
</html>
